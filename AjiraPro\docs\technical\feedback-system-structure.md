# Feedback System Structure

## Overview
The FajiraPro resume analysis system provides separate, structured feedback from both Claude and OpenAI to give users specific, actionable recommendations for improving their resumes.

## Database Schema

### Separate Feedback Columns
- `claude_feedback` (JSONB) - Format and structure recommendations from <PERSON>
- `openai_feedback` (JSONB) - Content quality recommendations from OpenAI  
- `scoring_feedback` (JSONB) - Combined feedback (maintained for backward compatibility)

### Feedback Storage Structure

#### Claude Feedback Format
```json
{
  "format_issues": [
    {
      "issue": "Non-standard date format",
      "severity": "medium",
      "recommendation": "Use consistent MM/YYYY format for all dates",
      "section": "experience"
    },
    {
      "issue": "Graphics detected",
      "severity": "high",
      "recommendation": "Remove images and graphics for ATS compatibility",
      "section": "header"
    }
  ],
  "structure_issues": [
    {
      "issue": "Missing contact information",
      "severity": "high", 
      "recommendation": "Add phone number and email address",
      "section": "contact_info"
    },
    {
      "issue": "Non-standard section headings",
      "severity": "medium",
      "recommendation": "Use standard headings like 'Work Experience' instead of 'Professional Journey'",
      "section": "headings"
    }
  ],
  "ats_compatibility": {
    "score": 75,
    "issues": ["Complex formatting", "Non-standard fonts", "Multiple columns"],
    "recommendations": [
      "Use simple formatting without tables or text boxes",
      "Stick to standard fonts like Arial, Calibri, or Times New Roman",
      "Use single-column layout for better ATS parsing"
    ]
  }
}
```

#### OpenAI Feedback Format
```json
{
  "career_overview": [
    {
      "issue": "Vague professional summary",
      "severity": "medium",
      "recommendation": "Add specific years of experience and key technical skills",
      "section": "professional_summary",
      "current_text": "Experienced developer with strong skills...",
      "suggested_improvement": "Software Developer with 5+ years of experience in Python, React, and cloud technologies, specializing in scalable web applications..."
    }
  ],
  "experience": [
    {
      "issue": "Lack of quantified achievements",
      "severity": "high",
      "recommendation": "Add metrics and numbers to demonstrate impact",
      "section": "Software Engineer at TechCorp",
      "current_text": "Improved system performance",
      "suggested_improvement": "Improved system performance by 40%, reducing load times from 3s to 1.8s"
    },
    {
      "issue": "Missing action verbs",
      "severity": "medium",
      "recommendation": "Start bullet points with strong action verbs",
      "section": "Data Analyst at DataCorp",
      "current_text": "Was responsible for data analysis",
      "suggested_improvement": "Analyzed complex datasets to identify trends and insights"
    }
  ],
  "education": [
    {
      "issue": "Missing relevant coursework",
      "severity": "low",
      "recommendation": "Add relevant coursework for entry-level positions",
      "section": "Bachelor's Degree"
    }
  ],
  "skills": [
    {
      "issue": "Missing relevant technical skills",
      "severity": "medium",
      "recommendation": "Add trending technologies in your field",
      "section": "technical_skills",
      "suggested_additions": ["Docker", "Kubernetes", "AWS", "TypeScript"]
    },
    {
      "issue": "Outdated technologies listed",
      "severity": "low", 
      "recommendation": "Remove or de-emphasize outdated technologies",
      "section": "technical_skills",
      "outdated_items": ["Flash", "Internet Explorer compatibility"]
    }
  ],
  "content_quality": [
    {
      "issue": "Inconsistent tense usage",
      "severity": "low",
      "recommendation": "Use past tense for previous roles, present tense for current role",
      "section": "experience"
    },
    {
      "issue": "Grammatical errors detected",
      "severity": "medium",
      "recommendation": "Review and correct grammatical errors",
      "section": "multiple",
      "examples": ["'recieve' should be 'receive'", "'there' should be 'their'"]
    }
  ]
}
```

## Feedback Issue Structure

### FeedbackIssue Model
```python
class FeedbackIssue(BaseModel):
    issue: str                    # Description of the issue
    severity: str                 # "low", "medium", "high"
    recommendation: str           # Specific fix recommendation
    section: Optional[str]        # Resume section affected
    current_text: Optional[str]   # Current problematic text
    suggested_improvement: Optional[str]  # Suggested replacement
```

### Severity Levels
- **High**: Critical issues that significantly impact resume effectiveness
- **Medium**: Important issues that should be addressed for better results
- **Low**: Minor improvements that can enhance overall quality

## Implementation Workflow

### Claude Feedback Generation
1. **Format Analysis**: Evaluate fonts, layout, structure
2. **ATS Compatibility Check**: Assess parsing-friendliness
3. **Issue Identification**: Find format and structure problems
4. **Recommendation Generation**: Create specific fix suggestions
5. **Severity Assignment**: Prioritize issues by impact

### OpenAI Feedback Generation  
1. **Content Analysis**: Evaluate parsed JSON content
2. **Category Assessment**: Analyze each resume section
3. **Quality Evaluation**: Check grammar, clarity, impact
4. **Gap Identification**: Find missing or weak content
5. **Improvement Suggestions**: Generate specific enhancements

## Frontend Display Strategy

### Feedback Presentation
```typescript
interface FeedbackDisplay {
  claudeFeedback: {
    formatIssues: FeedbackIssue[];
    structureIssues: FeedbackIssue[];
    atsCompatibility: ATSAnalysis;
  };
  openAIFeedback: {
    careerOverview: FeedbackIssue[];
    experience: FeedbackIssue[];
    education: FeedbackIssue[];
    skills: FeedbackIssue[];
    contentQuality: FeedbackIssue[];
  };
}
```

### User Interface Components
1. **Feedback Tabs**: Separate tabs for "Format & ATS" and "Content Quality"
2. **Issue Cards**: Individual cards for each feedback issue
3. **Severity Indicators**: Color-coded severity levels (red=high, yellow=medium, green=low)
4. **Before/After Examples**: Show current text vs. suggested improvements
5. **Progress Tracking**: Mark issues as "addressed" or "ignored"

## API Endpoints

### Get Feedback
```
GET /api/resumes/{id}/feedback
Response: {
  claude_feedback: ClaudeFeedback,
  openai_feedback: OpenAIFeedback,
  combined_summary: string[]
}
```

### Get Feedback by Type
```
GET /api/resumes/{id}/feedback/claude
GET /api/resumes/{id}/feedback/openai
```

## Database Migration Required

Run the additional SQL script to add separate feedback columns:

```sql
-- Add separate feedback columns
ALTER TABLE resumes 
ADD COLUMN IF NOT EXISTS claude_feedback JSONB,
ADD COLUMN IF NOT EXISTS openai_feedback JSONB;

-- Update scoring_history table
ALTER TABLE scoring_history 
ADD COLUMN IF NOT EXISTS claude_feedback JSONB,
ADD COLUMN IF NOT EXISTS openai_feedback JSONB;
```

## Benefits of Separate Feedback

### For Users
- **Clear Categorization**: Understand format vs. content issues
- **Targeted Improvements**: Focus on specific areas
- **Actionable Recommendations**: Specific, implementable suggestions
- **Progress Tracking**: See improvements in each category

### For Development
- **Modular Updates**: Update Claude or OpenAI feedback independently
- **A/B Testing**: Test different feedback approaches
- **Analytics**: Track which feedback types are most effective
- **Maintenance**: Easier to debug and improve specific feedback types

### For Business
- **User Engagement**: More detailed feedback increases value perception
- **Conversion**: Specific recommendations drive premium feature usage
- **Retention**: Users return to implement suggestions
- **Differentiation**: Comprehensive feedback sets apart from competitors

This structured approach ensures users receive comprehensive, actionable feedback that helps them create more effective resumes while providing clear value for the FajiraPro service.
