-- =====================================================
-- ADD SEPARATE FEEDBACK COLUMNS FOR CLAUDE AND OPENAI
-- Run these commands in the Supabase SQL Editor
-- =====================================================

-- STEP 1: Add separate feedback columns to resumes table
-- =====================================================
ALTER TABLE resumes 
ADD COLUMN IF NOT EXISTS claude_feedback JSONB,
ADD COLUMN IF NOT EXISTS openai_feedback JSONB;

-- STEP 2: Update scoring_history table to include separate feedback columns
-- =====================================================
ALTER TABLE scoring_history 
ADD COLUMN IF NOT EXISTS claude_feedback JSONB,
ADD COLUMN IF NOT EXISTS openai_feedback JSONB;

-- STEP 3: Update the save_scoring_history function to handle separate feedback
-- =====================================================
CREATE OR REPLACE FUNCTION public.save_scoring_history()
RETURNS TRIGGER AS $$
BEGIN
  -- Save to history if parsing or scoring fields have changed
  IF (
    -- Parsing completed
    (NEW.parsed_content IS NOT NULL AND OLD.parsed_content IS DISTINCT FROM NEW.parsed_content) OR
    -- Scoring completed
    (NEW.overall_score IS NOT NULL AND (
      OLD.overall_score IS DISTINCT FROM NEW.overall_score OR
      OLD.claude_score IS DISTINCT FROM NEW.claude_score OR
      OLD.openai_score IS DISTINCT FROM NEW.openai_score OR
      OLD.career_overview_score IS DISTINCT FROM NEW.career_overview_score OR
      OLD.experience_score IS DISTINCT FROM NEW.experience_score OR
      OLD.education_score IS DISTINCT FROM NEW.education_score OR
      OLD.additional_qualifications_score IS DISTINCT FROM NEW.additional_qualifications_score OR
      OLD.content_quality_score IS DISTINCT FROM NEW.content_quality_score OR
      OLD.claude_feedback IS DISTINCT FROM NEW.claude_feedback OR
      OLD.openai_feedback IS DISTINCT FROM NEW.openai_feedback))
  ) THEN
    
    INSERT INTO public.scoring_history (
      resume_id,
      parsed_content,
      parsing_version,
      overall_score,
      claude_score,
      openai_score,
      career_overview_score,
      experience_score,
      education_score,
      additional_qualifications_score,
      content_quality_score,
      scoring_feedback,
      claude_feedback,
      openai_feedback,
      scoring_version
    ) VALUES (
      NEW.id,
      NEW.parsed_content,
      '1.0',
      NEW.overall_score,
      NEW.claude_score,
      NEW.openai_score,
      NEW.career_overview_score,
      NEW.experience_score,
      NEW.education_score,
      NEW.additional_qualifications_score,
      NEW.content_quality_score,
      NEW.scoring_feedback,
      NEW.claude_feedback,
      NEW.openai_feedback,
      '1.0'
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 4: Add comments for the new feedback columns
-- =====================================================
COMMENT ON COLUMN resumes.claude_feedback IS 'Format and structure recommendations from Claude analysis';
COMMENT ON COLUMN resumes.openai_feedback IS 'Content quality recommendations from OpenAI analysis';
COMMENT ON COLUMN resumes.scoring_feedback IS 'Combined feedback from both Claude and OpenAI (for backward compatibility)';

-- STEP 5: Verify the new columns were added
-- =====================================================
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'resumes' 
AND column_name IN ('claude_feedback', 'openai_feedback', 'scoring_feedback');

-- Verify scoring_history table has the new columns
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'scoring_history' 
AND column_name IN ('claude_feedback', 'openai_feedback', 'scoring_feedback');

-- STEP 6: Example of how the feedback will be structured
-- =====================================================

-- Claude Feedback Example (Format & Structure Issues):
/*
{
  "format_issues": [
    {
      "issue": "Non-standard date format",
      "severity": "medium",
      "recommendation": "Use consistent MM/YYYY format for all dates",
      "section": "experience"
    },
    {
      "issue": "Graphics detected",
      "severity": "high", 
      "recommendation": "Remove images and graphics for ATS compatibility",
      "section": "header"
    }
  ],
  "structure_issues": [
    {
      "issue": "Missing contact information",
      "severity": "high",
      "recommendation": "Add phone number and email address",
      "section": "contact_info"
    }
  ],
  "ats_compatibility": {
    "score": 75,
    "issues": ["Complex formatting", "Non-standard fonts"],
    "recommendations": ["Use simple formatting", "Stick to standard fonts like Arial or Calibri"]
  }
}
*/

-- OpenAI Feedback Example (Content Quality Issues):
/*
{
  "career_overview": [
    {
      "issue": "Vague professional summary",
      "severity": "medium",
      "recommendation": "Add specific years of experience and key technical skills",
      "current_text": "Experienced developer...",
      "suggested_improvement": "Software Developer with 5+ years of experience in Python, React, and cloud technologies..."
    }
  ],
  "experience": [
    {
      "issue": "Lack of quantified achievements",
      "severity": "high",
      "recommendation": "Add metrics and numbers to demonstrate impact",
      "section": "Software Engineer at TechCorp",
      "example": "Instead of 'Improved system performance', use 'Improved system performance by 40%, reducing load times from 3s to 1.8s'"
    }
  ],
  "skills": [
    {
      "issue": "Missing relevant technical skills",
      "severity": "medium", 
      "recommendation": "Add trending technologies in your field",
      "suggested_additions": ["Docker", "Kubernetes", "AWS"]
    }
  ],
  "content_quality": [
    {
      "issue": "Inconsistent tense usage",
      "severity": "low",
      "recommendation": "Use past tense for previous roles, present tense for current role"
    }
  ]
}
*/
