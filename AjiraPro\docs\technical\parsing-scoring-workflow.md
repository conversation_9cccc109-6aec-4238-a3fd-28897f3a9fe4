# Resume Parsing + Scoring Workflow

## Overview
The FajiraPro resume analysis system follows a comprehensive workflow with <PERSON> handling both parsing and format scoring, while OpenAI handles content scoring:

1. **Stage 1 - <PERSON> Parsing**: Extract structured content from PDF/DOCX files
2. **Stage 2 - Claude Format Scoring**: Analyze format, structure, and ATS compatibility
3. **Stage 3 - OpenAI Content Scoring**: Analyze parsed content for quality and relevance

This approach ensures consistent, high-quality analysis with specialized AI models for each task.

## Complete Workflow Architecture

```
User Upload → File Storage → Claude Parsing → Claude Format Scoring → OpenAI Content Scoring → User Results
     ↓              ↓              ↓                ↓                      ↓                    ↓
File Stored    parsing_status   parsed_content   claude_score         openai_scores      Complete Analysis
              = 'pending'      (JSON format)    + recommendations    + recommendations   + Resume Preview
```

## Stage 1: Resume Parsing (<PERSON>)

### Purpose
- Extract structured content from resume files (PDF/DOCX)
- Standardize format for consistent analysis
- Handle various resume layouts and formats

### Process
1. **File Processing**: Extract text from PDF/DOCX
2. **Claude Analysis**: Parse content into structured JSON
3. **Data Storage**: Save parsed content to `parsed_content` field
4. **Status Tracking**: Update `parsing_status` and `parsed_at`

### Parsed Content Structure
```json
{
  "personal_info": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "location": "City, State",
    "linkedin": "linkedin.com/in/johndoe"
  },
  "professional_summary": "Brief professional overview...",
  "experience": [
    {
      "title": "Software Engineer",
      "company": "Tech Corp",
      "location": "City, State",
      "start_date": "2020-01",
      "end_date": "2023-12",
      "description": "Job responsibilities and achievements...",
      "achievements": ["Achievement 1", "Achievement 2"]
    }
  ],
  "education": [
    {
      "degree": "Bachelor of Science",
      "field": "Computer Science",
      "institution": "University Name",
      "graduation_date": "2020-05",
      "gpa": "3.8"
    }
  ],
  "skills": {
    "technical": ["Python", "JavaScript", "React"],
    "soft": ["Leadership", "Communication"],
    "tools": ["Git", "Docker", "AWS"]
  },
  "certifications": [
    {
      "name": "AWS Certified Developer",
      "issuer": "Amazon",
      "date": "2023-06",
      "expiry": "2026-06"
    }
  ],
  "projects": [
    {
      "name": "Project Name",
      "description": "Project description...",
      "technologies": ["React", "Node.js"],
      "url": "github.com/user/project"
    }
  ],
  "languages": [
    {
      "language": "English",
      "proficiency": "Native"
    }
  ],
  "additional_sections": {
    "volunteer_work": [],
    "publications": [],
    "awards": []
  }
}
```

### Parsing Status Values
- `pending`: Resume uploaded, parsing not started
- `parsing`: Claude is currently parsing the resume
- `parsed`: Parsing completed successfully
- `failed`: Parsing failed (error stored in `parsing_error`)

## Stage 2: Claude Format Scoring

### Purpose
- Analyze resume format, structure, and ATS compatibility
- Generate format-specific recommendations
- Provide ATS optimization suggestions

### Process
1. **Format Analysis**: Evaluate layout, fonts, and structure
2. **ATS Compatibility Check**: Assess parsing-friendliness
3. **Score Calculation**: Generate format score (25% of overall)
4. **Recommendations**: Create format improvement suggestions

### Format Scoring Criteria
- **Structure & Layout**: Reverse chronological format, clear sections
- **ATS Compatibility**: Simple fonts, no graphics/tables, standard formatting
- **Contact Information**: Proper placement and completeness
- **Consistency**: Date formats, bullet points, spacing

## Stage 3: OpenAI Content Scoring

### Purpose
- Analyze parsed content for quality and relevance
- Evaluate content across multiple categories
- Generate content improvement recommendations

### Process
1. **Input Preparation**: Receive `parsed_content` from Claude
2. **Content Analysis**: Evaluate across 5 categories (75% of overall)
3. **Score Calculation**: Compute weighted content scores
4. **Feedback Generation**: Create content improvement suggestions

### Scoring Categories

#### Claude Analysis (25% of overall score)
- **Format & Structure**: ATS compatibility, layout, formatting
- **Section Organization**: Proper headings, logical flow
- **Contact Information**: Completeness and placement
- **Date Formatting**: Consistency and clarity

#### OpenAI Analysis (75% of overall score)
- **Career Overview (25%)**: Professional summary and skills
- **Experience (40%)**: Work history, achievements, progression
- **Education (15%)**: Degrees, certifications, relevance
- **Additional Qualifications (10%)**: Projects, languages, extras
- **Content Quality (10%)**: Grammar, clarity, professionalism

### Scoring Status Values
- `pending`: Parsing completed, scoring not started
- `scoring`: AI models are analyzing the content
- `scored`: Scoring completed successfully
- `failed`: Scoring failed (error stored in `scoring_error`)

## Database Schema

### Enhanced Resumes Table
```sql
-- Parsing fields
parsed_content JSONB,                    -- Structured content from Claude
parsing_status TEXT DEFAULT 'pending',   -- parsing, parsed, failed
parsed_at TIMESTAMP,                     -- When parsing completed
parsing_error TEXT,                      -- Error message if failed

-- Scoring fields
overall_score DECIMAL(5,2),              -- Final weighted score (0-100)
claude_score DECIMAL(5,2),               -- Format analysis score
openai_score DECIMAL(5,2),               -- Content analysis score
career_overview_score DECIMAL(5,2),     -- Career section score
experience_score DECIMAL(5,2),          -- Experience section score
education_score DECIMAL(5,2),           -- Education section score
additional_qualifications_score DECIMAL(5,2), -- Additional quals score
content_quality_score DECIMAL(5,2),     -- Content quality score
scoring_feedback JSONB,                 -- Detailed feedback
scoring_status TEXT DEFAULT 'pending',   -- scoring, scored, failed
last_scored_at TIMESTAMP,               -- When scoring completed
scoring_error TEXT                      -- Error message if failed
```

### Scoring History Table
Tracks both parsing and scoring changes over time for improvement analysis.

## Complete API Workflow

### 1. Resume Upload
```
POST /api/resumes/upload
→ File stored in Supabase Storage
→ Resume record created with parsing_status='pending'
→ Claude parsing task queued
```

### 2. Claude Parsing Process
```
Celery Task: parse_resume_task
→ Extract text from PDF/DOCX file
→ Call Claude API for content parsing
→ Update parsed_content and parsing_status='parsed'
→ Queue Claude format scoring task
```

### 3. Claude Format Scoring Process
```
Celery Task: score_format_task
→ Use parsed_content and original file for format analysis
→ Call Claude API for format/structure scoring
→ Update claude_score and format recommendations
→ Queue OpenAI content scoring task
```

### 4. OpenAI Content Scoring Process
```
Celery Task: score_content_task
→ Use parsed_content as structured input
→ Call OpenAI API for content analysis across 5 categories
→ Calculate category scores and overall OpenAI score
→ Update all scoring fields and scoring_status='scored'
→ Calculate final overall_score (weighted average)
```

### 5. Results Retrieval
```
GET /api/resumes/{id}
→ Returns complete resume data including:
  - parsed_content (structured JSON data)
  - claude_score (format & structure)
  - openai category scores
  - overall_score (weighted final score)
  - scoring_feedback (recommendations from both AI models)
  - parsing/scoring status
```

## Error Handling

### Parsing Errors
- File corruption or unsupported format
- Claude API failures or rate limits
- Text extraction issues

### Scoring Errors
- Missing parsed content
- OpenAI/Claude API failures
- Invalid score calculations

### Recovery Mechanisms
- Automatic retries with exponential backoff
- Fallback to alternative parsing methods
- Graceful degradation for partial failures

## Performance Considerations

### Optimization Strategies
- **Parallel Processing**: Parse and score different resumes simultaneously
- **Caching**: Cache parsed content to avoid re-parsing
- **Batch Processing**: Group similar operations
- **Rate Limiting**: Respect AI service limits

### Monitoring
- Track parsing/scoring success rates
- Monitor API response times
- Alert on high failure rates
- Cost tracking for AI services

## Security & Privacy

### Data Protection
- Parsed content contains sensitive personal information
- RLS policies ensure user data isolation
- Automatic cleanup of old parsing data
- Secure API key management

### Compliance
- GDPR compliance for EU users
- Data retention policies
- User consent for AI processing
- Audit trails for data access

## Future Enhancements

### Planned Improvements
- **Multi-language Support**: Parse resumes in different languages
- **Industry-specific Parsing**: Tailored parsing for different fields
- **Real-time Processing**: Faster parsing and scoring
- **Advanced Analytics**: Trend analysis and benchmarking

This workflow ensures high-quality, consistent resume analysis while maintaining performance and security standards.
