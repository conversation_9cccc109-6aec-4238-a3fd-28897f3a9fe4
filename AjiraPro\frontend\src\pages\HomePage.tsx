import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import AuthModal from '../components/AuthModal';
import { Button } from '../components/ui';

// Icons for the CTA cards
const CreateIcon = ({ className }: { className?: string }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
  </svg>
);

const TailorIcon = ({ className }: { className?: string }) => (
  <svg className={className} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
  </svg>
);



const HomePage: React.FC = () => {
  const { isDark } = useTheme();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'create' | 'tailor'>('create');

  const openAuthModal = () => {
    setAuthModalOpen(true);
  };

  const closeAuthModal = () => {
    setAuthModalOpen(false);
  };

  return (
    <div className={`min-h-screen ${isDark ? 'bg-dark-100' : 'bg-gray-50'}`}>
      {/* Hero Section */}
      <section className={`${isDark ? 'bg-gradient-to-r from-dark-300 to-dark-500 border-t border-b border-dark-400' : 'bg-gradient-to-r from-blue-600 to-indigo-700'} text-white py-16 md:py-20`}>
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              ATS-Optimized Resume Solutions That Land You Interviews
            </h1>
            <p className="text-xl mb-12">
              Score your resume against ATS systems, get AI-powered optimization suggestions, and tailor your resume to specific job descriptions to maximize your interview chances.
            </p>

            {/* Primary CTA Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
              {/* Card 1: Create from Scratch */}
              <motion.div
                className={`rounded-lg p-6 shadow-lg transition-all duration-300 hover:shadow-xl flex flex-col h-full ${
                  isDark
                    ? 'bg-dark-200 border border-dark-300 hover:border-neon-cyan'
                    : 'bg-white hover:border-blue-300 border border-transparent'
                }`}
                whileHover={{ y: -5 }}
              >
                <div className="mb-4">
                  <CreateIcon className={`w-12 h-12 mx-auto ${isDark ? 'text-neon-cyan' : 'text-blue-600'}`} />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  ATS Score & Optimize Resume
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Upload your resume for ATS scoring and get AI-powered optimization suggestions
                </p>
                <div className="mt-auto">
                  <Link to="/resume-optimizer" className="block">
                    <Button
                      variant="primary"
                      fullWidth
                    >
                      Score & Optimize
                    </Button>
                  </Link>
                </div>
              </motion.div>

              {/* Card 2: Tailor Existing Document */}
              <motion.div
                className={`rounded-lg p-6 shadow-lg transition-all duration-300 hover:shadow-xl flex flex-col h-full ${
                  isDark
                    ? 'bg-dark-200 border border-dark-300 hover:border-neon-magenta'
                    : 'bg-white hover:border-purple-300 border border-transparent'
                }`}
                whileHover={{ y: -5 }}
              >
                <div className="mb-4">
                  <TailorIcon className={`w-12 h-12 mx-auto ${isDark ? 'text-neon-magenta' : 'text-purple-600'}`} />
                </div>
                <h3 className={`text-xl font-semibold mb-2 ${isDark ? 'text-white' : 'text-gray-800'}`}>
                  Tailor to Job Description
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Upload your resume and get a tailored version with matching cover letter
                </p>
                <div className="mt-auto">
                  <Link to="/tailor-document" className="block">
                    <Button
                      variant="primary"
                      fullWidth
                    >
                      Tailor Now
                    </Button>
                  </Link>
                </div>
              </motion.div>


            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className={`text-3xl font-bold text-center mb-12 ${isDark ? 'text-white' : ''}`}>Why Choose AjiraPro?</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className={`${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow-md'} p-6 rounded-lg`}>
              <div className={`${isDark ? 'text-neon-cyan' : 'text-blue-600'} text-4xl mb-4`}>
                <i className="fas fa-robot"></i>
              </div>
              <h3 className={`text-xl font-bold mb-2 ${isDark ? 'text-white' : ''}`}>AI-Powered Content</h3>
              <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                Our AI engine generates professional content tailored to your experience and target job.
              </p>
            </div>
            <div className={`${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow-md'} p-6 rounded-lg`}>
              <div className={`${isDark ? 'text-neon-magenta' : 'text-blue-600'} text-4xl mb-4`}>
                <i className="fas fa-check-circle"></i>
              </div>
              <h3 className={`text-xl font-bold mb-2 ${isDark ? 'text-white' : ''}`}>ATS-Optimized</h3>
              <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                Get past Applicant Tracking Systems with our optimized templates and keyword analysis.
              </p>
            </div>
            <div className={`${isDark ? 'bg-dark-200 shadow-[0_4px_20px_rgba(0,0,0,0.3)]' : 'bg-white shadow-md'} p-6 rounded-lg`}>
              <div className={`${isDark ? 'text-neon-blue' : 'text-blue-600'} text-4xl mb-4`}>
                <i className="fas fa-file-alt"></i>
              </div>
              <h3 className={`text-xl font-bold mb-2 ${isDark ? 'text-white' : ''}`}>Professional Templates</h3>
              <p className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                Choose from a variety of professional, modern templates designed to impress recruiters.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className={`py-16 ${isDark ? 'bg-dark-200' : 'bg-gray-100'}`}>
        <div className="container mx-auto px-4">
          <h2 className={`text-3xl font-bold text-center mb-8 ${isDark ? 'text-white' : ''}`}>How It Works</h2>

          {/* Tabs for the two paths */}
          <div className="flex justify-center mb-12">
            <div className={`flex p-1 rounded-lg ${isDark ? 'bg-dark-300' : 'bg-gray-200'}`}>
              <button
                onClick={() => setActiveTab('create')}
                className={`px-4 py-2 mx-1 rounded-md transition-colors duration-300 ${
                  activeTab === 'create'
                    ? isDark
                      ? 'bg-dark-400 text-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.2)]'
                      : 'bg-white text-blue-600 shadow'
                    : isDark
                      ? 'text-gray-400 hover:text-gray-200'
                      : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                ATS Score & Optimize
              </button>
              <button
                onClick={() => setActiveTab('tailor')}
                className={`px-4 py-2 mx-1 rounded-md transition-colors duration-300 ${
                  activeTab === 'tailor'
                    ? isDark
                      ? 'bg-dark-400 text-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.2)]'
                      : 'bg-white text-blue-600 shadow'
                    : isDark
                      ? 'text-gray-400 hover:text-gray-200'
                      : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Tailor to Job
              </button>
            </div>
          </div>

          {/* Tab content for "ATS Score & Optimize" */}
          {activeTab === 'create' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className={`text-center p-6 rounded-lg transition-all duration-300 bg-opacity-50
                              ${isDark ? 'bg-dark-300' : 'bg-white shadow'}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold mb-4
                                ${isDark
                                  ? 'bg-dark-400 text-neon-cyan border border-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                  : 'bg-blue-100 text-blue-600'}`}>
                  1
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : ''}`}>
                  Upload your resume
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Upload your existing resume in PDF or DOCX format
                </p>
                <div className={`rounded-lg p-5 ${isDark ? 'bg-dark-400' : 'bg-gray-100'}`}>
                  <div className="flex justify-center mb-4">
                    <svg className={`w-10 h-10 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16h6v-6h4l-7-7-7 7h4v6zm-4 2h14v2H5v-2z" />
                    </svg>
                  </div>
                  <p className={`text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Simply upload your current resume and our system will automatically parse and analyze its content for ATS compatibility.
                  </p>
                  <div className={`mt-3 w-16 h-1 mx-auto rounded ${isDark ? 'bg-neon-cyan bg-opacity-50' : 'bg-blue-300'}`}></div>
                </div>
              </div>

              {/* Step 2 */}
              <div className={`text-center p-6 rounded-lg transition-all duration-300 bg-opacity-50
                              ${isDark ? 'bg-dark-300' : 'bg-white shadow'}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold mb-4
                                ${isDark
                                  ? 'bg-dark-400 text-neon-cyan border border-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                  : 'bg-blue-100 text-blue-600'}`}>
                  2
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : ''}`}>
                  Get your ATS score
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Receive a detailed ATS compatibility score and analysis
                </p>
                <div className={`rounded-lg p-5 ${isDark ? 'bg-dark-400' : 'bg-gray-100'}`}>
                  <div className="flex justify-center mb-4">
                    <svg className={`w-10 h-10 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 7h3V5h-3v5zm-3 0H6V5h3v5zm0 9H6v-5h3v5zm6 0h-3v-5h3v5zm3-4h-3v-5h3v5z" />
                    </svg>
                  </div>
                  <p className={`text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Our AI evaluates your resume against ATS algorithms, identifying formatting issues, missing keywords, and other factors that might prevent your resume from reaching hiring managers.
                  </p>
                  <div className={`mt-3 w-16 h-1 mx-auto rounded ${isDark ? 'bg-neon-cyan bg-opacity-50' : 'bg-blue-300'}`}></div>
                </div>
              </div>

              {/* Step 3 */}
              <div className={`text-center p-6 rounded-lg transition-all duration-300 bg-opacity-50
                              ${isDark ? 'bg-dark-300' : 'bg-white shadow'}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold mb-4
                                ${isDark
                                  ? 'bg-dark-400 text-neon-cyan border border-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                  : 'bg-blue-100 text-blue-600'}`}>
                  3
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : ''}`}>
                  Optimize and download
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Apply AI-powered optimization suggestions and download your improved resume
                </p>
                <div className={`rounded-lg p-5 ${isDark ? 'bg-dark-400' : 'bg-gray-100'}`}>
                  <div className="flex justify-center mb-4">
                    <svg className={`w-10 h-10 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                    </svg>
                  </div>
                  <p className={`text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Review AI-suggested improvements, apply the changes you want, and download your ATS-optimized resume in multiple formats ready to pass through any applicant tracking system.
                  </p>
                  <div className={`mt-3 w-16 h-1 mx-auto rounded ${isDark ? 'bg-neon-cyan bg-opacity-50' : 'bg-blue-300'}`}></div>
                </div>
              </div>
            </div>
          )}

          {/* Tab content for "Tailor to Job" */}
          {activeTab === 'tailor' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Step 1 */}
              <div className={`text-center p-6 rounded-lg transition-all duration-300 bg-opacity-50
                              ${isDark ? 'bg-dark-300' : 'bg-white shadow'}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold mb-4
                                ${isDark
                                  ? 'bg-dark-400 text-neon-cyan border border-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                  : 'bg-blue-100 text-blue-600'}`}>
                  1
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : ''}`}>
                  Upload document & job description
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Upload your existing resume and paste job description or provide the job posting URL
                </p>
                <div className={`rounded-lg p-5 ${isDark ? 'bg-dark-400' : 'bg-gray-100'}`}>
                  <div className="flex justify-center mb-4">
                    <svg className={`w-10 h-10 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16h6v-6h4l-7-7-7 7h4v6zm-4 2h14v2H5v-2z" />
                    </svg>
                  </div>
                  <p className={`text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Simply upload your existing resume and either paste the job description or provide the URL. Our system will handle the rest.
                  </p>
                  <div className={`mt-3 w-16 h-1 mx-auto rounded ${isDark ? 'bg-neon-cyan bg-opacity-50' : 'bg-blue-300'}`}></div>
                </div>
              </div>

              {/* Step 2 */}
              <div className={`text-center p-6 rounded-lg transition-all duration-300 bg-opacity-50
                              ${isDark ? 'bg-dark-300' : 'bg-white shadow'}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold mb-4
                                ${isDark
                                  ? 'bg-dark-400 text-neon-cyan border border-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                  : 'bg-blue-100 text-blue-600'}`}>
                  2
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : ''}`}>
                  AI analyzes and tailors content
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  AI identifies keywords and tailors your document to match the job
                </p>
                <div className={`rounded-lg p-5 ${isDark ? 'bg-dark-400' : 'bg-gray-100'}`}>
                  <div className="flex justify-center mb-4">
                    <svg className={`w-10 h-10 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
                    </svg>
                  </div>
                  <p className={`text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Our AI identifies key requirements and skills from the job posting and intelligently adjusts your resume to highlight relevant experience.
                  </p>
                  <div className={`mt-3 w-16 h-1 mx-auto rounded ${isDark ? 'bg-neon-cyan bg-opacity-50' : 'bg-blue-300'}`}></div>
                </div>
              </div>

              {/* Step 3 */}
              <div className={`text-center p-6 rounded-lg transition-all duration-300 bg-opacity-50
                              ${isDark ? 'bg-dark-300' : 'bg-white shadow'}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full text-xl font-bold mb-4
                                ${isDark
                                  ? 'bg-dark-400 text-neon-cyan border border-neon-cyan shadow-[0_0_10px_rgba(0,255,255,0.3)]'
                                  : 'bg-blue-100 text-blue-600'}`}>
                  3
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${isDark ? 'text-white' : ''}`}>
                  Download tailored documents
                </h3>
                <p className={`mb-4 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                  Download your tailored resume and matching cover letter
                </p>
                <div className={`rounded-lg p-5 ${isDark ? 'bg-dark-400' : 'bg-gray-100'}`}>
                  <div className="flex justify-center mb-4">
                    <svg className={`w-10 h-10 ${isDark ? 'text-neon-cyan' : 'text-blue-500'}`} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                    </svg>
                  </div>
                  <p className={`text-center ${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                    Get both a customized resume and a matching cover letter specifically optimized for the position you're applying for.
                  </p>
                  <div className={`mt-3 w-16 h-1 mx-auto rounded ${isDark ? 'bg-neon-cyan bg-opacity-50' : 'bg-blue-300'}`}></div>
                </div>
              </div>
            </div>
          )}


        </div>
      </section>

      {/* CTA Section */}
      <section className={`py-16 ${isDark ? 'bg-dark-300 border-t border-dark-400' : 'bg-blue-600'} text-white`}>
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Land Your Dream Job?</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Join thousands of job seekers who have successfully used AjiraPro to create ATS-optimized and job-tailored resumes.
          </p>
          <button
            onClick={openAuthModal}
            className={`${isDark ? 'bg-dark-400 text-neon-cyan border border-neon-cyan hover:bg-dark-500 shadow-[0_0_15px_rgba(0,255,255,0.4)]' : 'bg-white text-blue-600 hover:bg-gray-100 shadow-lg'} font-bold py-3 px-8 rounded-lg transition duration-300`}
          >
            Get Started Now
          </button>
        </div>
      </section>

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={closeAuthModal}
        initialMode="signup"
      />
    </div>
  );
};

export default HomePage;
