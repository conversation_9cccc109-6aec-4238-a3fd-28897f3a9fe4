Coding Standards
General Practices
•	Write self-documenting code with clear variable/function names
•	Keep functions small and focused on a single task
•	Follow language-specific style guides
•	Document all public APIs and complex logic
•	Use TypeScript interfaces and Python type hints
•	Implement comprehensive error handling
Security Practices
Authentication
•	Supabase Auth: Use JWT tokens stored in HTTP-only cookies
•	Password Policies: Enforce 12+ characters, special characters, and numbers
•	OAuth Providers: Enable Google/GitHub with Supabase RLS
Data Protection
•	Encryption: Encrypt sensitive fields (e.g., phone numbers,passwords) using pgcrypto
•	Row-Level Security (RLS): Restrict database access by user ID
•	Input Sanitization: Use Pydantic (backend) and TypeScript (frontend)
Payment Security
•	Flutterwave Webhooks: Verify payloads with verif-hash
•	No Card Storage: Rely on Flutterwave tokens; never store card details (phone number can be stored)
File Security
•	Validate all uploaded files for: 
o	MIME type (only accept .docx and .pdf)
o	Size limits (max 5MB)
o	Malware scanning before processing
•	Use signed URLs with short expiration for file downloads
Frontend (React/TypeScript)
Code Organization
•	Functional components with hooks
•	Component structure: 
o	Props interface at top
o	State and hooks next
o	Helper functions
o	Return statement with JSX
TypeScript Guidelines
•	Use strict typing for API responses and state management
•	Create interfaces for all data structures
•	Use union types for state variations
•	Avoid any type unless absolutely necessary
Component Design
•	Create reusable UI components
•	Use React Context for state that spans multiple components
•	Implement proper loading/error states for async operations
CSS/Styling
•	Use Tailwind CSS utility classes
•	Create consistent spacing with predefined values
•	Follow mobile-first responsive design principles
•	Use Framer Motion for consistent animations
Backend (FastAPI/Python)
Code Organization
•	Routers for endpoint groups
•	Services for business logic
•	Models for data validation
•	Core modules for configuration
API Design
•	Use RESTful principles
•	Implement versioning (/api/v1/...)
•	Return consistent error responses
•	Document with OpenAPI/Swagger
Validation
•	Pydantic models for all request/response schemas
•	Validate all inputs, including query parameters
•	Use dependency injection for auth and common parameters
Async/Await
•	Use async for I/O-bound operations
•	Implement proper exception handling in async functions
•	Use background tasks for non-critical operations
AI/ML Code
Prompt Engineering
•	Structure prompts with clear sections and instructions
•	Include examples of desired output format
•	Set consistent temperature values (0.2-0.7 range)
Data Processing
•	Clean and normalize inputs before processing
•	Implement retries for API calls
•	Cache common responses and patterns
Error Handling
•	Graceful fallback if AI services are unavailable
•	Log original inputs for debugging
•	Implement rate limiting and quota management
Testing Standards
Frontend
•	Jest for unit tests
•	React Testing Library for component tests
•	Cypress for end-to-end tests
Backend
•	Pytest for unit and integration tests
•	Mock external dependencies (Supabase, OpenAI)
•	Test error conditions and edge cases
Coverage Goals
•	80% code coverage for critical modules
•	Test all API endpoints
•	Test all form validations

*NOTE* Assess to make sure no conflicts with versions, security and compatibility.

