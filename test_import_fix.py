#!/usr/bin/env python3
"""
Test script to verify that the validate_resume_task import fix works
"""

import sys
import os

# Add the backend directory to Python path
backend_dir = os.path.join(os.path.dirname(__file__), 'AjiraPro', 'backend')
sys.path.insert(0, backend_dir)

def test_import():
    """Test if validate_resume_task can be imported successfully"""
    try:
        print("Testing import of validate_resume_task...")
        
        # This should work now that we've added the function
        from app.tasks.resume_analysis import validate_resume_task
        
        print("✅ SUCCESS: validate_resume_task imported successfully!")
        print(f"Function: {validate_resume_task}")
        print(f"Function name: {validate_resume_task.__name__}")
        print(f"Function doc: {validate_resume_task.__doc__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ OTHER ERROR: {e}")
        return False

def test_other_tasks():
    """Test if other tasks can still be imported"""
    try:
        print("\nTesting import of other tasks...")
        
        from app.tasks.resume_analysis import parse_resume_task, score_format_task, analyze_resume_task
        
        print("✅ SUCCESS: All other tasks imported successfully!")
        print(f"- parse_resume_task: {parse_resume_task.__name__}")
        print(f"- score_format_task: {score_format_task.__name__}")
        print(f"- analyze_resume_task: {analyze_resume_task.__name__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ OTHER ERROR: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("TESTING RESUME ANALYSIS IMPORT FIX")
    print("=" * 60)
    
    # Test the main fix
    success1 = test_import()
    
    # Test that we didn't break other imports
    success2 = test_other_tasks()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 ALL TESTS PASSED! The import fix is working correctly.")
        print("\nThe error 'cannot import name validate_resume_task' should now be resolved.")
    else:
        print("❌ SOME TESTS FAILED. Please check the implementation.")
    print("=" * 60)
