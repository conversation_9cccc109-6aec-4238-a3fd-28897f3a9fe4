API Specifications
Authentication Endpoints
## Authentication
- **POST** /api/auth/register: Register a new user (uses Supabase Auth under the hood)
- **POST** /api/auth/login: existing User login and JWT issuance.
## Resume Generation & Revamp
- **POST** /api/resume/create: Create a new resume (step-by-step builder)
- **POST** /api/resume/revamp: Upload and revamp existing resume
- **POST** /api/resume/tailor: Tailor a resume to a job listing
- **GET** /api/resume/{resume_id}: Get resume data and status
- **GET** /api/resume/{resume_id}/download: Get resume download link
## Payments
- **POST** /api/payment/webhook: Webhook for Flutterwave payment notifications (internal)
- **GET** /api/payment/status/{resume_id} : Check payment status for a resume
## Templates
- **GET** /api/templates: Get available resume templates
## Admin  (Protected)
GET /api/admin/jobs: Get all job processing statuses (paginated)
GET /api/admin/stats: Get system statistics
Error Responses: 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found,500 Internal Server Error

