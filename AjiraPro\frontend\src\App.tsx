import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';

// Testing CI/CD pipeline - This comment should trigger the frontend workflow

// Import pages
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import DashboardPage from './pages/DashboardPage';
import ProfilePage from './pages/ProfilePage';
import OnboardingPage from './pages/OnboardingPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
import ResumeOptimizerPage from './pages/ResumeOptimizerPage';
import ResumeResultsPage from './pages/ResumeResultsPage';
import MagicLinkSuccess from './components/MagicLinkSuccess';
import ExpiredToken from './components/ExpiredToken';
import ResetPasswordRedirect from './components/ResetPasswordRedirect';
import GoogleCallback from './components/GoogleCallback';

// Import components
import Header from './components/Header';
import Footer from './components/Footer';
import ProtectedRoute from './components/ProtectedRoute';

// Import contexts
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <Router>
          <div className="min-h-screen flex flex-col transition-colors duration-300 dark:bg-dark-100 dark:text-white">
            <Header />
            <main className="flex-grow">
              <Routes>
                {/* Public routes */}
                {/* Home page with reset password redirect capability */}
                <Route path="/" element={
                  <ResetPasswordRedirect fallback={<HomePage />} />
                } />
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<RegisterPage />} />
                <Route path="/forgot-password" element={<ForgotPasswordPage />} />
                <Route path="/reset-password" element={<ResetPasswordPage />} />
                <Route path="/resume-optimizer" element={<ResumeOptimizerPage />} />
                <Route path="/resume-results" element={<ResumeResultsPage />} />
                <Route path="/auth/magic-link" element={<MagicLinkSuccess />} />
                <Route path="/auth/token-expired" element={<ExpiredToken />} />
                <Route path="/auth/google-callback" element={<GoogleCallback />} />

                {/* Protected routes */}
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <DashboardPage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <ProfilePage />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/onboarding"
                  element={
                    <ProtectedRoute>
                      <OnboardingPage />
                    </ProtectedRoute>
                  }
                />

                {/* Fallback route */}
                <Route path="*" element={<HomePage />} />
              </Routes>
            </main>
            <Footer />
          </div>
        </Router>
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
