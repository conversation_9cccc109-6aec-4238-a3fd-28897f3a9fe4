from fastapi import Depends
from typing import List, Optional
from ..models.resume import ResumeCreate, ResumeUpdate, ResumeResponse
from .supabase import supabase
from uuid import UUID
import json

class ResumeService:
    async def create_resume(self, resume: ResumeCreate, user_id: UUID) -> ResumeResponse:
        """
        Create a new resume for a user.
        """
        resume_data = resume.dict()
        resume_data["user_id"] = str(user_id)
        resume_data["status"] = "draft"
        
        # Convert complex objects to JSON strings for Supabase
        for field in ["personal_info", "education", "experience", "skills", "projects", 
                     "languages", "certifications", "references", "custom_sections"]:
            if field in resume_data and resume_data[field] is not None:
                resume_data[field] = json.dumps(resume_data[field])
        
        response = supabase.table("resumes").insert(resume_data).execute()
        
        if len(response.data) > 0:
            # Convert JSON strings back to objects
            for field in ["personal_info", "education", "experience", "skills", "projects", 
                         "languages", "certifications", "references", "custom_sections"]:
                if field in response.data[0] and response.data[0][field] is not None:
                    if isinstance(response.data[0][field], str):
                        response.data[0][field] = json.loads(response.data[0][field])
            
            return ResumeResponse(**response.data[0])
        
        return None

    async def get_resumes(self, user_id: UUID, skip: int = 0, limit: int = 100) -> List[ResumeResponse]:
        """
        Get all resumes for a user.
        """
        response = supabase.table("resumes").select("*").eq("user_id", str(user_id)).range(skip, skip + limit - 1).execute()
        
        resumes = []
        for resume_data in response.data:
            # Convert JSON strings to objects
            for field in ["personal_info", "education", "experience", "skills", "projects", 
                         "languages", "certifications", "references", "custom_sections"]:
                if field in resume_data and resume_data[field] is not None:
                    if isinstance(resume_data[field], str):
                        resume_data[field] = json.loads(resume_data[field])
            
            resumes.append(ResumeResponse(**resume_data))
        
        return resumes

    async def get_resume(self, resume_id: str, user_id: UUID) -> Optional[ResumeResponse]:
        """
        Get a specific resume by ID.
        """
        response = supabase.table("resumes").select("*, file_path AS pdf_storage_path").eq("id", resume_id).eq("user_id", str(user_id)).execute()
        
        if len(response.data) > 0:
            resume_data = response.data[0]
            
            # Convert JSON strings to objects
            for field in ["personal_info", "education", "experience", "skills", "projects", 
                         "languages", "certifications", "references", "custom_sections"]:
                if field in resume_data and resume_data[field] is not None:
                    if isinstance(resume_data[field], str):
                        resume_data[field] = json.loads(resume_data[field])
            
            return ResumeResponse(**resume_data)
        
        return None

    async def update_resume(self, resume_id: str, resume: ResumeUpdate, user_id: UUID) -> Optional[ResumeResponse]:
        """
        Update a specific resume by ID.
        """
        # First, check if the resume exists and belongs to the user
        existing_resume = await self.get_resume(resume_id, user_id)
        if not existing_resume:
            return None
        
        resume_data = resume.dict(exclude_unset=True)
        
        # Convert complex objects to JSON strings for Supabase
        for field in ["personal_info", "education", "experience", "skills", "projects", 
                     "languages", "certifications", "references", "custom_sections"]:
            if field in resume_data and resume_data[field] is not None:
                resume_data[field] = json.dumps(resume_data[field])
        
        response = supabase.table("resumes").update(resume_data).eq("id", resume_id).eq("user_id", str(user_id)).execute()
        
        if len(response.data) > 0:
            resume_data = response.data[0]
            
            # Convert JSON strings back to objects
            for field in ["personal_info", "education", "experience", "skills", "projects", 
                         "languages", "certifications", "references", "custom_sections"]:
                if field in resume_data and resume_data[field] is not None:
                    if isinstance(resume_data[field], str):
                        resume_data[field] = json.loads(resume_data[field])
            
            return ResumeResponse(**resume_data)
        
        return None

    async def delete_resume(self, resume_id: str, user_id: UUID) -> bool:
        """
        Delete a specific resume by ID.
        """
        # First, check if the resume exists and belongs to the user
        existing_resume = await self.get_resume(resume_id, user_id)
        if not existing_resume:
            return False
        
        response = supabase.table("resumes").delete().eq("id", resume_id).eq("user_id", str(user_id)).execute()
        
        return len(response.data) > 0

    async def generate_resume_content(self, resume_id: str, user_id: UUID, job_description: Optional[str] = None) -> Optional[ResumeResponse]:
        """
        Generate content for a resume using AI.
        """
        # First, check if the resume exists and belongs to the user
        existing_resume = await self.get_resume(resume_id, user_id)
        if not existing_resume:
            return None
        
        # TODO: Implement AI content generation using OpenAI
        # For now, just return the existing resume
        return existing_resume

    async def analyze_resume(self, resume_id: str, user_id: UUID, job_description: str) -> Optional[dict]:
        """
        Analyze a resume against a job description to get ATS score and suggestions.
        """
        # First, check if the resume exists and belongs to the user
        existing_resume = await self.get_resume(resume_id, user_id)
        if not existing_resume:
            return None
        
        # TODO: Implement resume analysis using OpenAI
        # For now, return a mock analysis
        return {
            "ats_score": 85,
            "keyword_match": 80,
            "missing_keywords": ["python", "machine learning"],
            "suggestions": [
                "Add more details about your Python experience",
                "Include machine learning projects or experience",
                "Quantify your achievements with metrics"
            ]
        }

    async def export_resume(self, resume_id: str, user_id: UUID, format: str = "pdf") -> Optional[str]:
        """
        Export a resume to PDF or other formats.
        """
        # First, check if the resume exists and belongs to the user
        existing_resume = await self.get_resume(resume_id, user_id)
        if not existing_resume:
            return None
        
        # TODO: Implement resume export
        # For now, return a mock URL
        return f"https://storage.googleapis.com/ajirapro/exports/{resume_id}.{format}"
