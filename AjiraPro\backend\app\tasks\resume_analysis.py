"""
Resume Analysis Celery Tasks

Handles asynchronous processing of resume parsing and scoring
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime
from uuid import UUID

from celery import current_task
from ..celery_worker import celery
from ..services.supabase import supabase
from ..services.document_processor import document_processor
from ..services.claude_parser import claude_parser
from ..services.resume_validator import ai_resume_validator

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def validate_resume_task(self, resume_id: str):
    """
    Validate document as resume using Gemini AI

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting resume validation for resume_id: {resume_id}")

        # Update validation status to validating
        supabase.table("resumes").update({
            "validation_status": "validating"
        }).eq("id", resume_id).execute()

        # Get resume record to get file path
        resume_response = supabase.table("resumes").select("file_path").eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        file_path = resume_response.data[0]["file_path"]
        if not file_path:
            raise Exception(f"No file path found for resume {resume_id}")

        # Download file from Supabase storage
        logger.info(f"Downloading file from Supabase: {file_path}")
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Validate document using Gemini AI
        filename = file_path.split('/')[-1]
        logger.info(f"Validating document as resume using Gemini AI: {filename}")

        try:
            # Check if GEMINI_API_KEY is available
            from ..core.config import settings
            if settings.GEMINI_API_KEY:
                logger.info("GEMINI_API_KEY found, attempting Gemini validation")
                validation_result = asyncio.run(ai_resume_validator.validate_document_as_resume(
                    file_response, filename
                ))

                logger.info(f"Gemini validation completed: {validation_result}")

                # Update database with validation results
                update_data = {
                    "validation_status": "completed",
                    "is_resume": validation_result["is_resume"],
                    "validation_likelihood": validation_result.get("likelihood"),
                    "validation_reason": validation_result.get("reason"),
                    "validated_at": datetime.now().isoformat(),
                    "validation_error": None
                }

            else:
                logger.warning("GEMINI_API_KEY not found, using fallback validation")
                # Use fallback validation
                update_data = {
                    "validation_status": "completed",
                    "is_resume": True,
                    "validation_likelihood": 95,
                    "validation_reason": "Fallback validation - GEMINI_API_KEY not configured",
                    "validated_at": datetime.now().isoformat(),
                    "validation_error": None
                }
                validation_result = {
                    "is_resume": True,
                    "likelihood": 95,
                    "reason": "Fallback validation - GEMINI_API_KEY not configured",
                    "confidence": "medium"
                }

        except Exception as validation_error:
            logger.error(f"Gemini validation failed: {str(validation_error)}")
            # Update with validation error
            update_data = {
                "validation_status": "failed",
                "validation_error": str(validation_error),
                "validated_at": datetime.now().isoformat()
            }
            supabase.table("resumes").update(update_data).eq("id", resume_id).execute()
            raise

        # Update database with validation results
        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully validated resume {resume_id}: {validation_result}")

        # If validation passed, automatically start parsing
        if validation_result["is_resume"]:
            logger.info(f"Document validated as resume, starting parsing for {resume_id}")
            parse_resume_task.delay(resume_id, file_path)

        return {
            "status": "success",
            "resume_id": resume_id,
            "validation_result": validation_result
        }

    except Exception as e:
        logger.error(f"Resume validation task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "validation_status": "failed",
            "validation_error": str(e),
            "validated_at": datetime.now().isoformat()
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def parse_resume_task(self, resume_id: str, file_path: str):
    """
    Parse resume content using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
        file_path: Path to the resume file in Supabase storage
    """
    try:
        logger.info(f"Starting resume parsing for resume_id: {resume_id}")

        # Update status to parsing
        supabase.table("resumes").update({
            "parsing_status": "parsing"
        }).eq("id", resume_id).execute()

        # Download file from Supabase storage
        logger.info(f"Downloading file from Supabase: {file_path}")
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Extract full text for Claude parsing
        filename = file_path.split('/')[-1]  # Get filename from path
        logger.info(f"Extracting full text from resume: {file_path}")
        extracted_data = document_processor.extract_text_from_file(file_response, filename)

        # Parse resume using Claude
        logger.info(f"Parsing resume content with Claude for resume_id: {resume_id}")
        parsed_content = asyncio.run(claude_parser.parse_resume_content(
            extracted_data["text"],
            filename
        ))

        # Update resume with parsed content
        update_data = {
            "parsed_content": json.dumps(parsed_content),
            "parsing_status": "parsed",
            "parsed_at": datetime.now().isoformat(),
            "parsing_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully parsed resume {resume_id}")

        # Queue format scoring task
        score_format_task.delay(resume_id)

        return {
            "status": "success",
            "resume_id": resume_id,
            "parsed_sections": list(parsed_content.keys())
        }

    except Exception as e:
        logger.error(f"Resume parsing task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "parsing_status": "failed",
            "parsing_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True, max_retries=3)
def score_format_task(self, resume_id: str):
    """
    Score resume format and structure using Claude Sonnet 3.5

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting format scoring for resume_id: {resume_id}")

        # Update scoring status
        supabase.table("resumes").update({
            "scoring_status": "scoring"
        }).eq("id", resume_id).execute()

        # Get resume data including parsed content
        resume_response = supabase.table("resumes").select(
            "parsed_content, file_path"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        resume_data = resume_response.data[0]
        parsed_content = json.loads(resume_data["parsed_content"])

        # Download original file to get text for format analysis
        file_path = resume_data["file_path"]
        file_response = supabase.storage.from_("resumes").download(file_path)

        if not file_response:
            raise Exception(f"Failed to download file from Supabase: {file_path}")

        # Extract text from document
        filename = file_path.split('/')[-1]
        extracted_data = document_processor.extract_text_from_file(file_response, filename)
        original_text = extracted_data["text"]

        # Score format using Claude
        logger.info(f"Scoring format with Claude for resume_id: {resume_id}")
        claude_score, claude_feedback = asyncio.run(claude_parser.score_format_and_structure(
            parsed_content,
            original_text
        ))

        # Update resume with Claude scoring results
        update_data = {
            "claude_score": claude_score,
            "claude_feedback": json.dumps(claude_feedback),
            "scoring_status": "scored",
            "last_scored_at": datetime.now().isoformat(),
            "scoring_error": None
        }

        supabase.table("resumes").update(update_data).eq("id", resume_id).execute()

        logger.info(f"Successfully scored format for resume {resume_id}, score: {claude_score}")

        return {
            "status": "success",
            "resume_id": resume_id,
            "claude_score": claude_score,
            "feedback_items": len(claude_feedback.get("format_issues", []))
        }

    except Exception as e:
        logger.error(f"Format scoring task failed for {resume_id}: {str(e)}")

        # Update status to failed
        supabase.table("resumes").update({
            "scoring_status": "failed",
            "scoring_error": str(e)
        }).eq("id", resume_id).execute()

        raise

@celery.task(bind=True)
def analyze_resume_task(self, resume_id: str):
    """
    Complete resume analysis workflow - validation, parsing and scoring

    Args:
        resume_id: UUID of the resume record
    """
    try:
        logger.info(f"Starting complete analysis for resume_id: {resume_id}")

        # Get resume record to check current status
        resume_response = supabase.table("resumes").select(
            "file_path, validation_status, is_resume"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            raise Exception(f"Resume {resume_id} not found")

        resume_data = resume_response.data[0]
        file_path = resume_data["file_path"]
        if not file_path:
            raise Exception(f"No file path found for resume {resume_id}")

        # Check if validation is already completed
        if resume_data.get("validation_status") == "completed":
            if resume_data.get("is_resume"):
                # Already validated as resume, start parsing directly
                logger.info(f"Resume {resume_id} already validated, starting parsing")
                parse_result = parse_resume_task.delay(resume_id, file_path)
                return {
                    "status": "started",
                    "resume_id": resume_id,
                    "parse_task_id": parse_result.id
                }
            else:
                # Already validated as not a resume
                return {
                    "status": "not_a_resume",
                    "resume_id": resume_id,
                    "message": "Document was previously validated as not a resume"
                }
        else:
            # Start with validation
            logger.info(f"Starting validation for resume {resume_id}")
            validation_result = validate_resume_task.delay(resume_id)
            return {
                "status": "started",
                "resume_id": resume_id,
                "validation_task_id": validation_result.id
            }

    except Exception as e:
        logger.error(f"Failed to start analysis for resume {resume_id}: {str(e)}")
        raise

@celery.task
def get_analysis_status(resume_id: str) -> Dict[str, Any]:
    """
    Get current analysis status for a resume

    Args:
        resume_id: UUID of the resume record

    Returns:
        Dict with current status information
    """
    try:
        resume_response = supabase.table("resumes").select(
            "parsing_status, scoring_status, parsed_at, last_scored_at, parsing_error, scoring_error"
        ).eq("id", resume_id).execute()

        if not resume_response.data:
            return {"error": f"Resume {resume_id} not found"}

        data = resume_response.data[0]

        return {
            "resume_id": resume_id,
            "parsing_status": data["parsing_status"],
            "scoring_status": data["scoring_status"],
            "parsed_at": data["parsed_at"],
            "last_scored_at": data["last_scored_at"],
            "parsing_error": data["parsing_error"],
            "scoring_error": data["scoring_error"],
            "overall_status": _determine_overall_status(data)
        }

    except Exception as e:
        logger.error(f"Failed to get analysis status for resume {resume_id}: {str(e)}")
        return {"error": str(e)}

def _determine_overall_status(data: Dict[str, Any]) -> str:
    """Determine overall analysis status based on validation, parsing and scoring status"""
    validation_status = data.get("validation_status", "pending")
    parsing_status = data.get("parsing_status", "pending")
    scoring_status = data.get("scoring_status", "pending")
    is_resume = data.get("is_resume")

    # Check validation status first
    if validation_status == "validating":
        return "validating"
    elif validation_status == "failed":
        return "validation_failed"
    elif validation_status == "completed" and is_resume is False:
        return "not_a_resume"
    elif validation_status == "completed" and is_resume is True:
        # Validation passed, check parsing status
        if parsing_status == "failed":
            return "parsing_failed"
        elif parsing_status == "parsing":
            return "parsing"
        elif parsing_status == "parsed":
            # Parsing done, check scoring status
            if scoring_status == "pending":
                return "ready_for_scoring"
            elif scoring_status == "scoring":
                return "scoring"
            elif scoring_status == "scored":
                return "completed"
            elif scoring_status == "failed":
                return "scoring_failed"
            else:
                return "ready_for_scoring"
        else:
            return "ready_for_parsing"
    else:
        return "pending"
